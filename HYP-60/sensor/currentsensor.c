// Standard C library header files
#include "ti/devices/cc13x2x7_cc26x2x7/driverlib/sys_ctrl.h"
#include <assert.h>
#include <stddef.h>
#include <string.h>
#include <stdint.h>
#include <stdio.h>
#include <math.h>

// OpenThread related header files
#include <openthread/config.h>
#include <openthread-core-config.h>
#include <openthread/coap.h>
#include <openthread/ip6.h>
#include <openthread/link.h>
#include <openthread/thread.h>
#include <openthread/instance.h>

// TI platform related header files
#include <platform/system.h>
#include <ti/devices/DeviceFamily.h>
#include DeviceFamily_constructPath(driverlib/aon_batmon.h)
#include DeviceFamily_constructPath(driverlib/aon_rtc.h)
#include DeviceFamily_constructPath(driverlib/aon_event.h)

// POSIX multithreading related header files
#include <unistd.h>
#include <time.h>
#include <sched.h>
#include <pthread.h>
#include <mqueue.h>

// TI driver related header files
#include <ti/drivers/Power.h>
#include <ti/drivers/GPIO.h>
#include <ti/drivers/UART2.h>
#include <ti/drivers/ADCBuf.h>
#include <ti/drivers/Board.h>
#include <ti/sysbios/BIOS.h>
#include <ti/drivers/apps/Button.h>
#include <ti/drivers/PWM.h>
#include <ti/drivers/power/PowerCC26X2.h>
#include <ti/devices/cc13x2_cc26x2/driverlib/flash.h>
#include <ti/sysbios/knl/Semaphore.h>

// OpenThread and TI-RTOS support header files
#include "otsupport/otrtosapi.h"
#include "otsupport/otinstance.h"
#include "ti_drivers_config.h"
#include "utils/code_utils.h"
#include "otstack/otstack.h"
#include "common/task_config.h"
#include "tiop_config.h"

// Hash related header files
#include "mbedtls/sha256.h"

// Sensor Controller related header files
#include <sensor/currentsensor.h>

/******************************************************************************
 * OTA Flash Configuration
 *****************************************************************************/

#define OTA_FLASH_START_ADDRESS  0x56000       // Start address for OTA flash
#define OTA_FLASH_END_ADDRESS    0xAC000      // End address for OTA flash
#define CONFIG_ADDRESS 0xAFFFC  // 指定的配置存储地址

// Variable to keep track of the current flash write address
static uint32_t block_number = 0;// Current block number

static uint8_t firmware_major = 0;
static uint8_t firmware_minor = 1;
static uint8_t firmware_patch = 1;

/******************************************************************************
 * ADC Configuration
 *****************************************************************************/

#define REFERENCE_VOLTAGE_V  3.3                    // ADC reference voltage (V)
#define ADC_RESOLUTION       12                     // ADC resolution in bits
#define ADC_MAX_VALUE        ((1 << ADC_RESOLUTION) - 1)  // 4095
#define STEP_SIZE            (REFERENCE_VOLTAGE_V / ADC_MAX_VALUE)  // Voltage step size per ADC unit

/******************************************************************************
 * Attribute Operations
 *****************************************************************************/

#define ATTR_READ     0x01   // Read-only attribute
#define ATTR_WRITE    0x02   // Write-only attribute
#define ATTR_REPORT   0x04   // Reportable attribute

/******************************************************************************
 * Sensor Processing Queue
 *****************************************************************************/

#define CurrentSENSOR_PROC_QUEUE_MAX_MSG 11  // Maximum number of messages in the processing queue

/******************************************************************************
 * Default Reporting Addresses
 *****************************************************************************/

// Default address for current sensor reporting
#ifndef TIOP_CurrentSENSOR_REPORTING_ADDRESS
#define TIOP_CurrentSENSOR_REPORTING_ADDRESS "ff02::1"
#endif

// Site-wide multicast address
#ifndef TIOP_SITE_WIDE_MULTICAST_ADDRESS
#define TIOP_SITE_WIDE_MULTICAST_ADDRESS "ff03::2"  // Mesh-local all-routers multicast
#endif

/******************************************************************************
 * COAP Configuration
 *****************************************************************************/

#define DEFAULT_COAP_HEADER_TOKEN_LEN 2  // Default COAP header token length

/******************************************************************************
 * Communication Mode Configuration
 *****************************************************************************/
#define ENABLE_BROADCAST_MODE 0 // 1: Broadcast mode, 0: Unicast to parent mode

/******************************************************************************
 * Definitions and Macros
 *****************************************************************************/

/* Default Port and ADC Channel */
#define CTSENSOR_OUT          0

/* ADC Buffer Size */
#define ADCBUFFERSIZE         32

#define WAITOTA              36

/******************************************************************************
 * Structures
 *****************************************************************************/

/* CoAP Attribute Descriptor */
typedef struct { const char* uriPath;     // Property URI
uint16_t type;// Resource type: read-only or read-write
uint8_t* pValue;// Pointer to the attribute status value
} attrDesc_t;

/* Current Sensor Processing Queue Message */
struct CurrentSensor_procQueueMsg { CurrentSensor_evt evt; // Current sensor processing queue message events
};

/******************************************************************************
 * Local variables
 *****************************************************************************/

/* The IPv6 address to send reports to */
static otIp6Address reportingAddress;

/* The port number to send reports to */
static uint16_t peerPort = OT_DEFAULT_COAP_PORT;

/* POSIX message queue for event delivery */
const char CurrentSensor_procQueueName[] = "cs_process";  // Message queue name
static mqd_t CurrentSensor_procQueueDesc;// Message Queue Descriptor

/* OpenThread stack thread call stack */
static char stack[TASK_CONFIG_CurrentSENSOR_TASK_STACK_SIZE];

/* CoAP Resources */
static otCoapResource coapResource;

/* CoAP Attribute Status */
static uint8_t attrCurrent[70] = {0}; // Buffer for CoAP attribute status:
                                      // 0-7: MAC地址 (8字节)
                                      // 8-55: 微伏值 (12个4字节值，共48字节)
                                      // 56: 供电模式标志 (0=非供电，1=供电)
                                      // 57: 传感器类型
                                      // 58-60: 固件版本 (major.minor.patch)
                                      // 61: 连续睡眠模式标志
                                      // 62: RSSI值 (1字节，信号强度)
                                      // 63-66: message_id (4字节，递增计数器)

static uint8_t type;

/* Message ID counter - 每次发送递增 */
static uint32_t message_id = 0;

/* CoAP Attribute Descriptor */
const attrDesc_t coapAttr = { CurrentSENSOR_Current_URI,
(ATTR_READ | ATTR_REPORT), attrCurrent, };

/* Save server setting status: 1 means the CoAP server has been set up */
static bool serverSetup;

/* Broadcast Address */
static otIp6Address siteWideMulticastAddress;

/* SHA256 Context for mbedTLS */
static mbedtls_sha256_context ctx;

/* Counters */
static int count = 0;
static int value_index = 0;
static uint32_t maxMicroVolts = 0; // 记录未通电模式下的最大微伏值

/* LUT校正相关变量 */
static uint32_t lut_size = 0;
static uint32_t* lut_x = NULL;
static uint32_t* lut_y = NULL;
static bool lut_initialized = false;

/* UART Handle */
static UART2_Handle uart;

/* Time settings */
static uint16_t sleepTime = 4; // Default sleep time between ADC reads when active

/* ADC Buffer for Samples */
static uint16_t buffer[ADCBUFFERSIZE];
static uint16_t buffer2[ADCBUFFERSIZE];

/* Device Status Indicators */
bool isPowered;
bool isOTA;
bool hasParent;
uint8_t WaitOta = 0;
/* Continuous sleep mode flag triggered by CONFIG_BUTTON_1 */
static bool continuousSleepActive = false;
/* Semaphore to block the task during continuous sleep */
static Semaphore_Handle continuousSleepSem = NULL;

/******************************************************************************
 * Function Prototype
 *****************************************************************************/
// Current sensor processing thread
static void* CurrentSensor_task(void *arg0);

void sendFirmwareVersionRequest(otInstance *aInstance);

void handleFirmwareBlockResponse(void *aContext, otMessage *aMessage,
                                 const otMessageInfo *aMessageInfo,
                                 otError aResult);

void requestNextBlock(otInstance *aInstance, uint32_t blockNum);

void ota_deinit();

// New button handler and init function prototypes
void configButtonIsrHandler(uint_least8_t index);
void configButton_init(void);

// Parent address function prototype
void getParentAddress(otIp6Address *aAddress);

// Current calculation function prototype
float calculateCurrentFromMicroVolts(uint32_t microVolts);

// LUT校正相关函数原型
void readLUTData(void);
uint32_t correct_uv(uint32_t raw_uv);

/******************************************************************************
 Local Functions
 *****************************************************************************/

/**
 * @brief 读取LUT表数据
 *
 * 该函数从指定的内存地址读取LUT表数据，包括表大小、X轴数据和Y轴数据
 * 只在type=5时调用此函数进行初始化
 */
void readLUTData(void)
{
    if (lut_initialized) {
        return; // 已经初始化过了
    }

    // 读取LUT数据
    lut_size = *(uint32_t*)0xAF080;
    lut_x = (uint32_t*)0xAF084;
    lut_y = (uint32_t*)(0xAF084 + lut_size * 4);

    lut_initialized = true;
}

/**
 * @brief 使用LUT进行微伏校正
 *
 * 该函数使用预先存储的LUT表对原始微伏值进行校正
 * 使用线性插值方法在LUT表中查找对应的校正值
 * 修复了整数除法导致的精度丢失问题，使用浮点数进行精确计算
 *
 * @param raw_uv 原始微伏值
 * @return 校正后的微伏值
 */
uint32_t correct_uv(uint32_t raw_uv)
{
    if (!lut_initialized || lut_size < 2) {
        return raw_uv;
    }

    // 边界条件处理
    if (raw_uv <= lut_x[0]) return lut_y[0];
    if (raw_uv >= lut_x[lut_size-1]) return lut_y[lut_size-1];

    // 在LUT表中查找对应的区间进行线性插值
    for (int i = 0; i < lut_size - 1; i++) {
        if (raw_uv >= lut_x[i] && raw_uv <= lut_x[i+1]) {
            uint32_t x0 = lut_x[i], x1 = lut_x[i+1];
            uint32_t y0 = lut_y[i], y1 = lut_y[i+1];

            // 避免除零错误
            if (x1 == x0) return y0;

            // 使用浮点数进行精确的线性插值计算
            // 公式: y = y0 + (y1 - y0) * (x - x0) / (x1 - x0)
            double x0_d = (double)x0;
            double x1_d = (double)x1;
            double y0_d = (double)y0;
            double y1_d = (double)y1;
            double raw_uv_d = (double)raw_uv;

            double result = y0_d + (y1_d - y0_d) * (raw_uv_d - x0_d) / (x1_d - x0_d);

            // 四舍五入并转换回uint32_t
            return (uint32_t)(result + 0.5);
        }
    }
    return raw_uv;
}

/**
 * @brief Puts the system into low power mode for a specified duration.
 *
 * This function puts the system to sleep for the duration specified by `sleepTime`.
 * It helps conserve power by suspending system activities during idle periods.
 */
void enterLowPowerMode(void)
{
    sleep(sleepTime); // Put the system to sleep for sleepTime seconds
    usleep(460000);
}

/**
 * @brief Blinks the LED by toggling its state.
 *
 * This function toggles the LED on and off with a delay of 1 second between the toggles.
 * It can be used to provide a visual indication, such as signaling a status or an event.
 */
void turnLed(void)
{
    GPIO_toggle(CONFIG_GPIO_PWM_1);
    usleep(100000); // 0.1s
    GPIO_toggle(CONFIG_GPIO_PWM_1);
}

/**
 * @brief Retrieves the parent IPv6 address and stores it in the provided address structure.
 *
 * This function gets the parent router's MAC address, constructs an IPv6 link-local address
 * using the `fe80::/64` prefix, and stores it in the provided `aAddress` structure. If the
 * parent information is unavailable, a default reporting address is used.
 *
 * @param aAddress Pointer to an `otIp6Address` structure to store the parent IPv6 address.
 */
void getParentAddress(otIp6Address *aAddress)
{
    otInstance *instance = OtInstance_get();
    otRouterInfo parentInfo;
    otError error = otThreadGetParentInfo(instance, &parentInfo);

    char ipv6Str[40]; // The maximum length of an IPv6 address is 39 characters plus a '\0'
    const uint8_t *mac = parentInfo.mExtAddress.m8;

    if (error == OT_ERROR_NONE && parentInfo.mExtAddress.m8[0] != 0)
    {
        uint8_t modifiedFirstByte = mac[0] ^ 0x02;
        // Use sprintf to combine the MAC address and the fe80::/64 prefix
        sprintf(ipv6Str, "fe80::%02x%02x:%02x%02x:%02x%02x:%02x%02x",
                modifiedFirstByte, mac[1], mac[2], mac[3], mac[4], mac[5],
                mac[6], mac[7]);
        OtRtosApi_lock();
        otIp6AddressFromString(ipv6Str, aAddress);
        OtRtosApi_unlock();

    }
    else
    {
        OtRtosApi_lock();
        // If there is no parent address, use the default address or handle the error
        otIp6AddressFromString(TIOP_CurrentSENSOR_REPORTING_ADDRESS, aAddress);
        OtRtosApi_unlock();
    }
}

/**
 * @brief Formats microvolts values into a binary string and writes them to the output buffer.
 *
 * This function converts the given microvolts values into a binary format and writes them
 * into the output buffer. The MAC address is added once at the beginning if not already present.
 * In non-powered mode, it stores each sample's average microvolt value at a position determined by count.
 * In powered mode, it directly stores the average microvolt value.
 *
 * @param microVolts    Pointer to the array of 32-bit microvolt values.
 * @param sampleCount   Number of samples in the array.
 * @param output        Pointer to the output buffer where the formatted data is stored.
 * @param outputSize    Size of the output buffer to avoid overflow.
 */
void formatSamplesToString(const uint32_t *microVolts, size_t sampleCount,
                           uint8_t *output, size_t outputSize)
{
    if (value_index == 0)
    {
        // Retrieve the MAC address from memory location 0xAF070
        uint32_t macPartMsb = HWREG(0xAF070);         // First 4 bytes (MSB of MAC)
        uint32_t macPartLsb = HWREG(0xAF070 + 4);    // Next 4 bytes (LSB of MAC)
        uint64_t macAddress = ((uint64_t)macPartMsb << 32) | macPartLsb;

        // Write the MAC address to the output
        output[value_index++] = (macAddress >> 56) & 0xFF; // MSB
        output[value_index++] = (macAddress >> 48) & 0xFF;
        output[value_index++] = (macAddress >> 40) & 0xFF;
        output[value_index++] = (macAddress >> 32) & 0xFF;
        output[value_index++] = (macAddress >> 24) & 0xFF;
        output[value_index++] = (macAddress >> 16) & 0xFF;
        output[value_index++] = (macAddress >> 8) & 0xFF;
        output[value_index++] = macAddress & 0xFF; // LSB
    }

    // 计算平均微伏值
    uint32_t avgMicroVolts = 0;
    if (sampleCount > 0) {
        for (size_t i = 0; i < sampleCount; i++) {
            avgMicroVolts += microVolts[i];
        }
        avgMicroVolts = avgMicroVolts / sampleCount;
    }

    // 对type=5进行LUT校正
    if (type == 5) {
        avgMicroVolts = correct_uv(avgMicroVolts);
    }
    
    if (isPowered)
    {
        // 在供电模式下，我们将同一个平均微伏值复制12份
        // 使用与非供电模式相同的格式，填满缓冲区
        for (int i = 0; i < 12; i++)
        {
            // 每次存储在不同位置，与非供电模式一致
            // 一个微伏值需要4个字节，所以偏移量是8（MAC）+ 4*i
            uint8_t offset = 8 + (i * 4);
            
            // 确保我们不会写出缓冲区边界
            if (offset + 4 <= outputSize)
            {
                // 将32位微伏值写入输出缓冲区(大端格式)
                output[offset] = (avgMicroVolts >> 24) & 0xFF; // 最高字节
                output[offset+1] = (avgMicroVolts >> 16) & 0xFF; 
                output[offset+2] = (avgMicroVolts >> 8) & 0xFF;
                output[offset+3] = avgMicroVolts & 0xFF;       // 最低字节
                
                // 更新value_index，如果这个位置更远
                if (offset + 4 > value_index) {
                    value_index = offset + 4;
                }
            }
        }
    }
    else
    {
        // 在非供电模式下，我们存储多个采样的微伏值
        // 每次存储在不同位置，根据count值确定
        // 一个微伏值需要4个字节，所以偏移量是8（MAC）+ 4*count
        uint8_t offset = 8 + (count * 4);
        
        // 确保我们不会写出缓冲区边界
        if (offset + 4 <= outputSize)
        {
            // 记录最大微伏值，用于杂波过滤
            if (avgMicroVolts > maxMicroVolts) {
                maxMicroVolts = avgMicroVolts;
            }
            
            // 将32位微伏值写入输出缓冲区(大端格式)
            output[offset] = (avgMicroVolts >> 24) & 0xFF; // 最高字节
            output[offset+1] = (avgMicroVolts >> 16) & 0xFF; 
            output[offset+2] = (avgMicroVolts >> 8) & 0xFF;
            output[offset+3] = avgMicroVolts & 0xFF;       // 最低字节
            
            // 更新value_index，如果这个位置更远
            if (offset + 4 > value_index) {
                value_index = offset + 4;
            }
        }
    }
}

/**
 * @brief Handles the CoAP response from the gateway.
 *
 * This function processes the response received from a CoAP request. If the response
 * contains a valid value, it updates the `sleepTime` accordingly.
 *
 * @param aContext      Pointer to the context (optional, can be NULL).
 * @param aMessage      Pointer to the received CoAP message.
 * @param aMessageInfo  Pointer to additional message information (source address, port).
 * @param aResult       The result of the CoAP transaction.
 */
// void CoapResponseHandler(void *aContext, otMessage *aMessage,
//                          const otMessageInfo *aMessageInfo, otError aResult)
// {
//     if (aResult == OT_ERROR_NONE)
//     {
//         uint16_t received_value;
//         otMessageRead(aMessage, otMessageGetOffset(aMessage), &received_value,
//                       sizeof(received_value));

//         // Store received value directly in sleepTime
//         if (received_value >= 0)
//             sleepTime = received_value; // Update sleep time from gateway

//     }

//     if (aMessage != NULL) {
//         otMessageFree(aMessage);
//     }
// }

/**
 * @brief 获取当前的RSSI值
 *
 * 该函数从OpenThread链路层获取最后一次接收的包的RSSI值
 * 如果无法获取，返回默认值-127
 *
 * @param instance OpenThread实例指针
 * @return int8_t RSSI值，单位是dBm
 */
int8_t getCurrentRssi(otInstance *instance)
{
    int8_t avgRssi = OT_RADIO_RSSI_INVALID;   // 先设成 127
    otError err;

    OtRtosApi_lock();
    err = otThreadGetParentAverageRssi(instance, &avgRssi);
    OtRtosApi_unlock();
    
    // 如果无法获取RSSI（返回OT_RADIO_RSSI_INVALID），则使用默认值
    if ((err != OT_ERROR_NONE) || (avgRssi == OT_RADIO_RSSI_INVALID)) {
        avgRssi = -127; // 使用一个默认值表示无效RSSI
    }
    
    return avgRssi;
}

/**
 * @brief Sends the current sensor data to the gateway using a CoAP POST request.
 *
 * This function creates a CoAP POST request with the current sensor data and sends it
 * to the gateway. It handles message creation, error checking, and sending the request.
 *
 * @param error           The OpenThread error status.
 * @param requestMessage  Pointer to the CoAP request message.
 * @param messageInfo     Structure containing the message destination information.
 * @param instance        Pointer to the OpenThread instance.
 */
void sendToGateway(otInstance *instance)
{
    otError error = OT_ERROR_NONE;
    otMessage *requestMessage = NULL;
    otMessageInfo messageInfo;

    value_index = 0;

    // 获取当前RSSI值
    int8_t rssi = getCurrentRssi(instance);

    // 递增消息ID
    message_id++;

    OtRtosApi_lock();
    requestMessage = otCoapNewMessage(instance, NULL);
    OtRtosApi_unlock();
    otEXPECT_ACTION(requestMessage != NULL, error = OT_ERROR_NO_BUFS);

    OtRtosApi_lock();
    otCoapMessageInit(requestMessage, OT_COAP_TYPE_NON_CONFIRMABLE, OT_COAP_CODE_POST);
    OtRtosApi_unlock();

    OtRtosApi_lock();
    otCoapMessageGenerateToken(requestMessage, DEFAULT_COAP_HEADER_TOKEN_LEN);
    OtRtosApi_unlock();

    OtRtosApi_lock();
    error = otCoapMessageAppendUriPathOptions(requestMessage, sleepAndSc_CURRENT_URI);
    OtRtosApi_unlock();
    otEXPECT(OT_ERROR_NONE == error);

    OtRtosApi_lock();
    error = otCoapMessageSetPayloadMarker(requestMessage);
    OtRtosApi_unlock();
    otEXPECT(OT_ERROR_NONE == error);

    // 添加RSSI和message_id到数据包
    attrCurrent[62] = rssi; // RSSI值
    // 添加message_id (4字节, 大端格式)
    attrCurrent[63] = (message_id >> 24) & 0xFF; // 最高字节
    attrCurrent[64] = (message_id >> 16) & 0xFF;
    attrCurrent[65] = (message_id >> 8) & 0xFF;
    attrCurrent[66] = message_id & 0xFF;        // 最低字节

    OtRtosApi_lock();
    error = otMessageAppend(requestMessage, attrCurrent, sizeof(attrCurrent));
    OtRtosApi_unlock();
    otEXPECT(OT_ERROR_NONE == error);

    memset(&messageInfo, 0, sizeof(messageInfo));
    messageInfo.mPeerAddr = reportingAddress;
    messageInfo.mPeerPort = peerPort;

// otCoapTxParameters txParams = {
//     .mAckTimeout = 1000,         // 单位：milliseconds
//     .mAckRandomFactorNumerator = 3,                     // 默认为1.5（3/2）
//     .mAckRandomFactorDenominator = 2,
//     .mMaxRetransmit = 1                                // 修改这里控制最大重传次数（默认是4）
// };

OtRtosApi_lock();
// if(isPowered){
    error = otCoapSendRequest(instance, requestMessage, &messageInfo, NULL, NULL);
// }else {
//     error = otCoapSendRequestWithParameters(instance, requestMessage, &messageInfo, CoapResponseHandler, NULL, &txParams);
// }
OtRtosApi_unlock();

exit:
    if (error != OT_ERROR_NONE && requestMessage != NULL)
    {
        OtRtosApi_lock();
        otMessageFree(requestMessage);
        OtRtosApi_unlock();
    }

    if (isPowered)
    {
        const char *msg = (error == OT_ERROR_NONE) ? "Send successfully!\r\n" : "Send failed\r\n";
        UART2_write(uart, msg, strlen(msg), NULL);
    }

    // 清空缓冲区
    memset(attrCurrent, 0, sizeof(attrCurrent));
    
    // 重置maxMicroVolts，确保下一轮记录始于0
    maxMicroVolts = 0;
}

/**
 * @brief Reports the current sensor data and manages ADC readings.
 *
 * This function reads the current sensor data using the ADC, formats the data,
 * and sends it to the gateway if necessary. It handles different scenarios based
 * on whether the device is powered or not, and manages low-power mode transitions.
 */
void CurrentSensorReport(void)
{
    otInstance *instance = OtInstance_get(); // Get OT instance

    if (!isPowered)
    {
        GPIO_write(CONFIG_GPIO_0, 1);

        usleep(500000);  // Wait 0.5 second to ensure the sensor is stable

        ADCBuf_init();

        ADCBuf_Handle adcBuf;
        ADCBuf_Params params;
        ADCBuf_Params_init(&params);
        params.returnMode = ADCBuf_RETURN_MODE_BLOCKING;
        params.recurrenceMode = ADCBuf_RECURRENCE_MODE_ONE_SHOT;
        params.samplingFrequency = 1600; // Hz - 采样频率

        // Open ADCBuf driver
        adcBuf = ADCBuf_open(CONFIG_ADCBUF_0, &params);
        // Setup conversion structure
        ADCBuf_Conversion conversion = { 0 };
        conversion.samplesRequestedCount = ADCBUFFERSIZE;
        conversion.sampleBuffer = buffer;
        conversion.adcChannel = CTSENSOR_OUT;

        // Start ADCBuf conversion
        ADCBuf_convert(adcBuf, &conversion, 1);
        
        // 创建微伏缓冲区
        uint32_t microVoltBuffer[ADCBUFFERSIZE];
        
        // 调整原始ADC值并转换为微伏
        ADCBuf_adjustRawValues(adcBuf, buffer, ADCBUFFERSIZE, CTSENSOR_OUT);
        ADCBuf_convertAdjustedToMicroVolts(adcBuf, CTSENSOR_OUT, buffer, microVoltBuffer, ADCBUFFERSIZE);

        // Close ADCBuf driver
        ADCBuf_close(adcBuf);

        GPIO_write(CONFIG_GPIO_0, 0);

        // 将当前微伏值添加到缓冲区中的相应位置
        formatSamplesToString(microVoltBuffer, ADCBUFFERSIZE, attrCurrent, sizeof(attrCurrent));
        
        count++;
        
        // If 12 readings have been collected, send the data to the gateway
        if (count >= 12)
        {  
            // 填写控制信息
            // 在固定位置57-61添加控制信息
            attrCurrent[56] = 0; // 非供电模式，12个微伏值
            attrCurrent[57] = type; // 指示传感器类型
            attrCurrent[58] = firmware_major;
            attrCurrent[59] = firmware_minor;
            attrCurrent[60] = firmware_patch;
            attrCurrent[61] = continuousSleepActive ? 1 : 0;

            // 只有当最大微伏值大于100μV (0.1mV)时才发送数据
            if (maxMicroVolts > 100) {
                sendToGateway(instance);
            }

            // 重置计数和缓冲区
            count = 0;
            value_index = 0;
            maxMicroVolts = 0; // 重置最大微伏值
            memset(attrCurrent, 0, sizeof(attrCurrent));
        }

        enterLowPowerMode(); // Entering low power mode based on sleepTime
    }
    else // isPowered == true
    {
        UART2_write(uart, "Start reading adc. . .\r\n",
                    strlen("Start reading adc. . .\r\n"), NULL);

        GPIO_write(CONFIG_GPIO_0, 1);

        usleep(500000);  // Wait 0.1 second to ensure the sensor is stable

        // One time init of ADCBuf driver
        ADCBuf_init();

        // Initialize optional ADCBuf parameters
        ADCBuf_Handle adcBuf;
        ADCBuf_Params params;
        ADCBuf_Params_init(&params);
        params.returnMode = ADCBuf_RETURN_MODE_BLOCKING;
        params.recurrenceMode = ADCBuf_RECURRENCE_MODE_ONE_SHOT;
        params.samplingFrequency = 1600; // Hz - 修改为400Hz用于测试

        // Open ADCBuf driver
        adcBuf = ADCBuf_open(CONFIG_ADCBUF_0, &params);
        // Setup conversion structure
        ADCBuf_Conversion conversion = { 0 };
        conversion.samplesRequestedCount = ADCBUFFERSIZE; // 修改为使用ADCBUFFERSIZE (100)
        conversion.sampleBuffer = buffer2;
        conversion.adcChannel = CTSENSOR_OUT;

        // Start ADCBuf conversion
        ADCBuf_convert(adcBuf, &conversion, 1);
        
        // 创建微伏缓冲区
        uint32_t microVoltBuffer[ADCBUFFERSIZE];
        
        // 调整原始ADC值并转换为微伏
        ADCBuf_adjustRawValues(adcBuf, buffer2, ADCBUFFERSIZE, CTSENSOR_OUT);
        ADCBuf_convertAdjustedToMicroVolts(adcBuf, CTSENSOR_OUT, buffer2, microVoltBuffer, ADCBUFFERSIZE);

        // Close ADCBuf driver
        ADCBuf_close(adcBuf);

        GPIO_write(CONFIG_GPIO_0, 0);

        // 计算平均电压和电流
        uint32_t sumMicroVolts = 0;
        for (int i = 0; i < ADCBUFFERSIZE; i++) {
            sumMicroVolts += microVoltBuffer[i];
        }
        uint32_t avgMicroVolts = sumMicroVolts / ADCBUFFERSIZE;
        float avgCurrentA = calculateCurrentFromMicroVolts(avgMicroVolts);
        
        // 仅输出平均值
        char outputBuffer[128];
        snprintf(outputBuffer, sizeof(outputBuffer), 
                "电压: %u μV (%.2f mV), 调整后电流: %.3f A\r\n",
                avgMicroVolts, avgMicroVolts/1000.0f, avgCurrentA);
        UART2_write(uart, outputBuffer, strlen(outputBuffer), NULL);

        // 发送平均微伏值到网关
        formatSamplesToString(microVoltBuffer, ADCBUFFERSIZE, attrCurrent, sizeof(attrCurrent));

        // 填写控制信息（与非供电模式一致）
        attrCurrent[56] = 1; // 供电模式，单微伏值
        attrCurrent[57] = type; // 指示传感器类型
        attrCurrent[58] = firmware_major;
        attrCurrent[59] = firmware_minor;
        attrCurrent[60] = firmware_patch;
        attrCurrent[61] = 0;

        UART2_write(uart, "Reading completed, sending. . .\r\n",
                    strlen("Reading completed, sending. . .\r\n"), NULL);

        sendToGateway(instance);

        enterLowPowerMode();
    }

    // If the device has a parent and is in OTA mode, request the firmware version
    if (hasParent && isOTA)
    {
        ota_deinit();
        sendFirmwareVersionRequest(OtInstance_get());
    }
    else if (isPowered && WaitOta > 0){
        WaitOta--;
        if(WaitOta == 0) {
            sendFirmwareVersionRequest(OtInstance_get());
        }
    }
    CurrentSensor_postEvt(CurrentSensor_evtReportCurrent);

}

/**
 * @brief Handles incoming CoAP requests and sends appropriate responses.
 *
 * This function processes incoming CoAP GET requests and responds with the current
 * attribute value. If an error occurs, it ensures that the response message is released.
 *
 * @param aContext      Pointer to the OpenThread instance (context).
 * @param aMessage      Pointer to the received CoAP message.
 * @param aMessageInfo  Pointer to additional message information (source address and port).
 */
void coapHandleServer(void *aContext, otMessage *aMessage,
                      const otMessageInfo *aMessageInfo)
{
    otError error = OT_ERROR_NONE;
    otMessage *responseMessage = NULL;
    otCoapCode messageCode = otCoapMessageGetCode(aMessage);

    // Handle CoAP GET request (Not useful at the moment)
    if (messageCode == OT_COAP_CODE_GET)
    {
        responseMessage = otCoapNewMessage((otInstance*) aContext, NULL);
        otEXPECT_ACTION(responseMessage != NULL, error = OT_ERROR_NO_BUFS);

        otCoapMessageInitResponse(responseMessage, aMessage,
                                  OT_COAP_TYPE_ACKNOWLEDGMENT,
                                  OT_COAP_CODE_CHANGED);
        otCoapMessageSetToken(responseMessage, otCoapMessageGetToken(aMessage),
                              otCoapMessageGetTokenLength(aMessage));
        otCoapMessageSetPayloadMarker(responseMessage);

        error = otMessageAppend(responseMessage, attrCurrent,
                                strlen((const char*) attrCurrent));
        otEXPECT(OT_ERROR_NONE == error);

        error = otCoapSendResponse((otInstance*) aContext, responseMessage,
                                   aMessageInfo);
        otEXPECT(OT_ERROR_NONE == error);
    }

    exit:
    // If an error occurs and the response message is not empty, release the message
    if (error != OT_ERROR_NONE && responseMessage != NULL)
    {
        otMessageFree(responseMessage);
    }
}

/**
 * @brief Prints a 32-byte hash value in hexadecimal format with a label.
 *
 * This function converts a 32-byte hash into a hexadecimal string and prints it
 * along with a descriptive label using UART.
 *
 * @param hash   Pointer to the 32-byte hash value.
 * @param label  Label describing the hash (e.g., "Computed Hash" or "Expected Hash").
 */
void print_hash(const uint8_t *hash, const char *label)
{
    // Temporary buffer to store the formatted string (including label and hash)
    char buffer[100];
    int offset = 0;

    // Write the label to the buffer
    offset = snprintf(buffer, sizeof(buffer), "%s: ", label);

    // Convert the hash bytes to a hexadecimal string and append to the buffer
    for (int i = 0; i < 32 && offset < sizeof(buffer) - 3; i++)
    {
        offset += snprintf(buffer + offset, sizeof(buffer) - offset, "%02x",
                           hash[i]);
    }

    // Add a newline to the end of the buffer
    snprintf(buffer + offset, sizeof(buffer) - offset, "\r\n");

    // Send the formatted string via UART
    UART2_write(uart, buffer, strlen(buffer), NULL);
}

/**
 * @brief Verifies the firmware hash by comparing it with the expected hash.
 *
 * This function computes the SHA-256 hash of the downloaded firmware and compares
 * it with the expected hash received from the gateway.
 *
 * @param firmware     Pointer to the firmware data (not used directly in this function).
 * @param length       Length of the firmware data (not used directly in this function).
 * @param expectedHash Pointer to the expected 32-byte hash.
 * @return true        If the computed hash matches the expected hash.
 * @return false       If the computed hash does not match or if an error occurs.
 */
bool verifyFirmwareHash(const uint8_t *firmware, size_t length,
                        const uint8_t *expectedHash)
{
    uint8_t computedHash[32];

    // Finish the SHA-256 computation and store the result in computedHash (32 bytes)
    if (mbedtls_sha256_finish_ret(&ctx, computedHash) != 0)
    {
        UART2_write(uart, "Error: Failed to finish SHA-256 computation\n",
                    strlen("Error: Failed to finish SHA-256 computation\n"),
                    NULL);
        mbedtls_sha256_free(&ctx);
        return false;  // Return false indicating a failure
    }

    // Free the SHA-256 context to release internal resources
    mbedtls_sha256_free(&ctx);

    // Print the computed hash for debugging purposes
    print_hash(computedHash, "Computed Hash");

    // Print the expected hash for debugging purposes
    print_hash(expectedHash, "Expected Hash");

    // Compare the computed hash with the expected hash (32 bytes)
    return memcmp(computedHash, expectedHash, 32) == 0;
}

/**
 * @brief Handles the firmware hash response received from the gateway.
 *
 * This function processes the CoAP message containing the firmware hash.
 * It verifies the received hash against the downloaded firmware and logs
 * the appropriate success or failure message. If the verification fails,
 * it retries the OTA process by requesting the first block again.
 *
 * @param aContext       Optional context pointer (unused in this function).
 * @param aMessage       Pointer to the received CoAP message.
 * @param aMessageInfo   Pointer to additional message information.
 * @param aResult        The result status of the CoAP transaction.
 */
void handleFirmwareHashResponse(void *aContext, otMessage *aMessage,
                                const otMessageInfo *aMessageInfo,
                                otError aResult)
{
    if (aResult == OT_ERROR_NONE)
    {
        UART2_write(uart, "Received firmware hash from gateway.\r\n",
                    strlen("Received firmware hash from gateway.\r\n"), NULL);

        // Buffer to store the received hash (assuming a 32-byte SHA-256 hash)
        uint8_t receivedHash[32];
        otMessageRead(aMessage, otMessageGetOffset(aMessage), receivedHash,
                      sizeof(receivedHash));

        uint8_t *firmwareData = (uint8_t*) OTA_FLASH_START_ADDRESS;
        size_t firmwareSize = block_number * 1024;

        // Verify the firmware hash
        if (verifyFirmwareHash(firmwareData, firmwareSize, receivedHash))
        {
            UART2_write(
                    uart,
                    "Firmware verified successfully! Preparing to reboot...\r\n",
                    strlen("Firmware verified successfully! Preparing to reboot...\r\n"),
                    NULL);

            // restart
            SysCtrlSystemReset();
        }
        else
        {
            UART2_write(
                    uart,
                    "Firmware verification failed! Aborting update.\r\n",
                    strlen("Firmware verification failed! Aborting update.\r\n"),
                    NULL);

            // restart
            SysCtrlSystemReset();
        }
    }
    else
    {
        char logBuffer[64];
        snprintf(logBuffer, sizeof(logBuffer), "Failed to receive hash: %d\r\n",
                 aResult);
        UART2_write(uart, logBuffer, strlen(logBuffer), NULL);
    }
}

/**
 * @brief Sends a request to the gateway to obtain the firmware hash.
 *
 * This function creates and sends a CoAP GET request to the gateway to retrieve
 * the firmware hash. It logs appropriate messages for success and failure cases.
 *
 * @param aInstance Pointer to the OpenThread instance.
 */
void requestFirmwareHashFromGateway(otInstance *aInstance)
{
    otError error;
    otMessage *message;
    otMessageInfo messageInfo;

    message = otCoapNewMessage(aInstance, NULL);
    if (message == NULL)
    {
        UART2_write(
                uart, "Failed to allocate CoAP message for hash request.\r\n",
                strlen("Failed to allocate CoAP message for hash request.\r\n"),
                NULL);
        return;
    }

    otCoapMessageInit(message, OT_COAP_TYPE_CONFIRMABLE, OT_COAP_CODE_GET);
    otCoapMessageGenerateToken(message, 2);
    otCoapMessageAppendUriPathOptions(message, "firmware/hash");     // Append the URI path option "firmware/hash" to specify the resource

    // Set up the message information (destination address and port)
    memset(&messageInfo, 0, sizeof(messageInfo));
    messageInfo.mPeerAddr = reportingAddress;  // Gateway IPv6 address
    messageInfo.mPeerPort = peerPort;          // CoAP destination port

    error = otCoapSendRequest(aInstance, message, &messageInfo,
                              handleFirmwareHashResponse, NULL);
    if (error != OT_ERROR_NONE)
    {
        char logBuffer[64];
        snprintf(logBuffer, sizeof(logBuffer),
                 "Failed to send hash request: %d\r\n", error);
        UART2_write(uart, logBuffer, strlen(logBuffer), NULL);
        otMessageFree(message);
    }
    else
    {
        UART2_write(uart, "Hash request sent to gateway.\r\n",
                    strlen("Hash request sent to gateway.\r\n"), NULL);
    }
}

/**
 * @brief Writes firmware data to flash memory with a page size of 4096 bytes.
 *
 * This function writes data to flash memory, ensuring the write operation does not
 * exceed the allocated flash size. It erases the flash page before writing if the
 * write operation starts at a new page boundary.
 *
 * @param write_address The starting address to write the data to in flash memory.
 * @param data          Pointer to the data to be written.
 * @param length        The length of the data to write in bytes.
 * @return otError      Returns OT_ERROR_NONE on success, or an error code on failure.
 */
otError Flash_WriteFirmware(uint32_t write_address, const uint8_t *data,
                            uint16_t length)
{
    uint32_t end_address = write_address + length;

    // Ensure the address does not exceed the allocated flash size
    if (end_address > OTA_FLASH_END_ADDRESS)
    {
        return OT_ERROR_INVALID_ARGS;
    }

    // Erase flash page if starting a new page
    if ((write_address % FlashSectorSizeGet()) == 0)
    {
        uint32_t status = FlashSectorErase(write_address);
        if (status != FAPI_STATUS_SUCCESS)
        {
            char errorMsg[128];
            snprintf(
                errorMsg,
                sizeof(errorMsg),
                "Error: Flash erase failed. Error code: %d, Address: 0x%08X\r\n",
                status, write_address
            );
            UART2_write(uart, errorMsg, strlen(errorMsg), NULL);
            return OT_ERROR_FAILED;
        }
    }

    // Write the data to flash
    uint8_t temp_buffer[4];
    for (uint32_t addr = write_address; addr < end_address;
            addr += 4, data += 4)
    {
        memcpy(temp_buffer, data, 4);
        uint32_t status = FlashProgram(temp_buffer, addr, 4);

        if (status != FAPI_STATUS_SUCCESS)
        {
            char errorMsg[128];
            snprintf(
                errorMsg,
                sizeof(errorMsg),
                "Error: Flash write failed. Error code: %d, Address: 0x%08X, Length: %u\r\n",
                status, addr, length
            );
            UART2_write(uart, errorMsg, strlen(errorMsg), NULL);
            return OT_ERROR_FAILED;
        }
    }

    return OT_ERROR_NONE;
}

/**
 * @brief Requests the next firmware block during the OTA update process.
 *
 * This function creates and sends a CoAP GET request to request the next firmware block
 * from the gateway. It includes the Block2 option to specify which block to retrieve.
 *
 * @param aInstance Pointer to the OpenThread instance.
 * @param blockNum  The block number to request.
 */
void requestNextBlock(otInstance *aInstance, uint32_t blockNum)
{
    otError error;
    otMessage *message;
    otMessageInfo messageInfo;

    // Create a CoAP GET request for the next block
    message = otCoapNewMessage(aInstance, NULL);
    if (message == NULL)
    {
        UART2_write(uart, "Failed to allocate CoAP message.\r\n",
                    strlen("Failed to allocate CoAP message.\r\n"), NULL);
        return;
    }

    otCoapMessageInit(message, OT_COAP_TYPE_CONFIRMABLE, OT_COAP_CODE_GET);
    otCoapMessageGenerateToken(message, 2);
    otCoapMessageAppendUriPathOptions(message, "firmware");

    // Add Block2 option for the next block
    uint32_t blockOption = (blockNum << 4) | (0 << 3) | (6);  // NUM, M, SZX
    otCoapMessageAppendUintOption(message, OT_COAP_OPTION_BLOCK2, blockOption);

    memset(&messageInfo, 0, sizeof(messageInfo));
    messageInfo.mPeerAddr = reportingAddress;  // Replace with gateway address
    messageInfo.mPeerPort = peerPort;

    error = otCoapSendRequest(aInstance, message, &messageInfo,
                              handleFirmwareBlockResponse, NULL);

    if (error != OT_ERROR_NONE)
    {
        char logBuffer[64];
        snprintf(logBuffer, sizeof(logBuffer),
                 "Failed to request block %d: %d\r\n", blockNum, error);
        UART2_write(uart, logBuffer, strlen(logBuffer), NULL);
        otMessageFree(message);
    }
    else
    {
        char logBuffer[64];
        snprintf(logBuffer, sizeof(logBuffer), "Requested block %d\r\n",
                 blockNum);
        UART2_write(uart, logBuffer, strlen(logBuffer), NULL);
    }
}

/**
 * @brief Initializes the device for Over-The-Air (OTA) updates.
 *
 * This function sets the OTA mode to true
 */
void ota_init()
{
    isOTA = true;
}

/**
 * @brief Deinitializes the OTA (Over-The-Air) update mode.
 *
 * This function disables the OTA mode by setting the `isOTA` flag to false,
 * indicating that the device has exited the OTA update process.
 */
void ota_deinit()
{
    isOTA = false;
}

/**
 * @brief Handles the response for a firmware block during OTA updates.
 *
 * This function processes the received CoAP message containing a firmware block,
 * manages block-by-block writing to flash, updates the SHA-256 hash, and determines
 * if more blocks are needed. It also handles specific messages such as "No update needed"
 * and "Update available."
 *
 * @param aContext      Context pointer (unused in this function).
 * @param aMessage      Pointer to the received CoAP message.
 * @param aMessageInfo  Pointer to message information (address and port).
 * @param aResult       The result of the CoAP message transaction.
 */
void handleFirmwareBlockResponse(void *aContext, otMessage *aMessage,
                                 const otMessageInfo *aMessageInfo,
                                 otError aResult)
{
    if (aResult == OT_ERROR_NONE)
    {
        uint16_t offset = otMessageGetOffset(aMessage);
        uint16_t length = otMessageGetLength(aMessage);
        uint16_t payloadLength = otMessageGetLength(aMessage) - offset;

        char logBuffer[128];
        snprintf(logBuffer, sizeof(logBuffer), "Offset: %u, Length: %u\r\n",
                 offset, length);
        UART2_write(uart, logBuffer, strlen(logBuffer), NULL);

        snprintf(logBuffer, sizeof(logBuffer), "Payload length: %u\r\n",
                 payloadLength);
        UART2_write(uart, logBuffer, strlen(logBuffer), NULL);

        if (payloadLength > 0)
        {
            uint8_t buffer[payloadLength+1];
            otMessageRead(aMessage, offset, buffer, payloadLength);
            buffer[payloadLength] = '\0';  // Ensure the buffer is null-terminated

            // Handle the "No update needed" message
            if (strcmp((char*) buffer, "No update needed") == 0)
            {
                UART2_write(
                        uart, "No update needed. Firmware is up-to-date.\r\n",
                        strlen("No update needed. Firmware is up-to-date.\r\n"),
                        NULL);
                //CurrentSensor_postEvt(CurrentSensor_evtReportCurrent);
                return;
            }

            if (strcmp((char*) buffer, "OTA busy") == 0)
            {
                WaitOta = WAITOTA;
                UART2_write(
                        uart, "OTA busy.\r\n",
                        strlen("OTA busy.\r\n"),
                        NULL);
                return;
            }

            // Handle the "Update available" message
            if (strcmp((char*) buffer, "Update available") == 0)
            {
                reportingAddress = aMessageInfo->mPeerAddr;

                UART2_write(
                        uart, "Update available. Starting OTA process.\r\n",
                        strlen("Update available. Starting OTA process.\r\n"),
                        NULL);

                // Initialize the SHA-256 context for firmware verification
                mbedtls_sha256_init(&ctx);
                if (mbedtls_sha256_starts_ret(&ctx, 0) != 0)
                {
                    UART2_write(
                            uart, "Error: Failed to start SHA-256 context\n",
                            strlen("Error: Failed to start SHA-256 context\n"),
                            NULL);
                    mbedtls_sha256_free(&ctx);
                    return;
                }

                // Initialize the block number and request the first block
                block_number = 0;
                requestNextBlock(OtInstance_get(), block_number);
                return;
            }

            // Handle firmware block data
            uint32_t write_address = OTA_FLASH_START_ADDRESS
                    + (block_number * 1024);
            // Write the firmware block to flash (skip the first 4 bytes containing block metadata)
            otError error = Flash_WriteFirmware(write_address, buffer + 4,
                                                payloadLength - 4);

            // Update the SHA-256 hash with the firmware block data
            if (mbedtls_sha256_update_ret(&ctx, buffer + 4, payloadLength - 4)
                    != 0)
            {
                UART2_write(
                        uart,
                        "Error: Failed to update SHA-256 hash with data\n",
                        strlen("Error: Failed to update SHA-256 hash with data\n"),
                        NULL);
                mbedtls_sha256_free(&ctx);
                return;
            }

            if (error != OT_ERROR_NONE)
            {
                char errorMsg[128];
                snprintf(
                        errorMsg,
                        sizeof(errorMsg),
                        "Error: Flash_WriteFirmware() failed. Error code: %d, Address: 0x%08X, Length: %u\r\n",
                        error, write_address, payloadLength - 4);
                UART2_write(uart, errorMsg, strlen(errorMsg), NULL);

                return;
            }

            UART2_write(uart, "Block written to flash.\r\n",
                        strlen("Block written to flash.\r\n"), NULL);

            // Parse block metadata (first 4 bytes of the buffer)
            uint16_t receivedBlockNumber = (buffer[0] << 8) | buffer[1]; // Combine high and low bytes
            bool moreBlocks = buffer[2];               // More blocks flag
            uint16_t blockSize = 1 << (buffer[3] + 4); // Block size (exponent to size)

            // Update the block number for the next request
            block_number = receivedBlockNumber + 1;

            // Log parsed Block2 information
            char logBuffer[128];
            snprintf(
                    logBuffer,
                    sizeof(logBuffer),
                    "Parsed Block2: blockNum=%u, moreBlocks=%d, blockSize=%u\r\n",
                    receivedBlockNumber, moreBlocks, blockSize);
            UART2_write(uart, logBuffer, strlen(logBuffer), NULL);

            // If more blocks are available, request the next block
            if (moreBlocks)
            {
                requestNextBlock(OtInstance_get(), block_number);
            }
            else
            {
                UART2_write(
                        uart,
                        "Firmware download complete. Requesting hash from gateway...\r\n",
                        strlen("Firmware download complete. Requesting hash from gateway...\r\n"),
                        NULL);

                // Request the firmware hash from the gateway for verification
                requestFirmwareHashFromGateway(OtInstance_get());
            }
        }
    }
    else
    {
        // Handle errors in receiving the firmware block
        char logBuffer[64];
        snprintf(logBuffer, sizeof(logBuffer),
                 "Failed to receive block: %d\r\n", aResult);
        UART2_write(uart, logBuffer, strlen(logBuffer), NULL);
    }
}

/**
 * @brief Sends a CoAP request to check the firmware version.
 *
 * This function creates and sends a CoAP GET request to the reporting address
 * to request the current firmware version. If the message fails to send, an error
 * message is logged via UART.
 *
 * @param aInstance Pointer to the OpenThread instance.
 */
void sendFirmwareVersionRequest(otInstance *aInstance)
{
    otError error;
    otMessage *message;
    otMessageInfo messageInfo;

    message = otCoapNewMessage(aInstance, NULL);
    if (message == NULL)
    {
        UART2_write(uart, "Failed to allocate CoAP message.\r\n",
                    strlen("Failed to allocate CoAP message.\r\n"), NULL);
        return;
    }

    otCoapMessageInit(message, OT_COAP_TYPE_CONFIRMABLE, OT_COAP_CODE_GET);
    otCoapMessageGenerateToken(message, 2);
    otCoapMessageAppendUriPathOptions(message, "version");

    char versionStr[16];
    snprintf(versionStr, sizeof(versionStr), "%d.%d.%d", firmware_major, firmware_minor, firmware_patch);

    char query[64];
    snprintf(query, sizeof(query), "version=%s", versionStr);
    otCoapMessageAppendUriQueryOption(message, query);

    memset(&messageInfo, 0, sizeof(messageInfo));
    messageInfo.mPeerAddr = reportingAddress;
    messageInfo.mPeerPort = peerPort;

    error = otCoapSendRequest(aInstance, message, &messageInfo,
                              handleFirmwareBlockResponse, NULL);
    if (error != OT_ERROR_NONE)
    {
        char logBuffer[64];
        snprintf(logBuffer, sizeof(logBuffer),
                 "Failed to send firmware version request: %d\r\n", error);
        UART2_write(uart, logBuffer, strlen(logBuffer), NULL);
        otMessageFree(message);
    }
}

/**
 * @brief Setting up the application CoAP server
 *
 * @param aInstance OT instance pointer
 * @param attr      Attribute data pointer
 *
 * @return If successful, it returns OT_ERROR_NONE; otherwise, it returns an error code
 */
otError setupCoapServer(otInstance *aInstance, const attrDesc_t *attr)
{
    otError error = OT_ERROR_NONE;

    OtRtosApi_lock();
    error = otCoapStart(aInstance, OT_DEFAULT_COAP_PORT);
    OtRtosApi_unlock();
    otEXPECT(OT_ERROR_NONE == error);

    if (attr->type & (ATTR_READ | ATTR_WRITE))
    {
        coapResource.mHandler = &coapHandleServer;
        coapResource.mUriPath = (const char*) attr->uriPath;
        coapResource.mContext = aInstance;

        OtRtosApi_lock();
        otCoapAddResource(aInstance, &(coapResource));
        OtRtosApi_unlock();
    }

    exit: return error;
}

/**
 * @brief Handling OT stack events
 *
 * @param event    Event Identifier
 * @param aContext Context pointer for the event
 *
 * @return NONE
 */
void processOtStackEvents(uint8_t event, void *aContext)
{
    (void) aContext; // Ignore unused parameters

    switch (event)
    {
    case OT_STACK_EVENT_NWK_JOINED:
    {
        CurrentSensor_postEvt(CurrentSensor_evtNwkJoined);
        break;
    }

    case OT_STACK_EVENT_NWK_JOINED_FAILURE:
    {
        CurrentSensor_postEvt(CurrentSensor_evtNwkJoinFailure);
        break;
    }

    case OT_STACK_EVENT_NWK_DATA_CHANGED:
    {
        CurrentSensor_postEvt(CurrentSensor_evtNwkSetup);
        break;
    }

    case OT_STACK_EVENT_DEV_ROLE_CHANGED:
    {
        CurrentSensor_postEvt(CurrentSensor_evtDevRoleChanged);
        break;
    }

    default:
    {
        break;
    }
    }
}

/**
 * @brief Handling Events
 *
 * @return NONE
 */
void processEvent(CurrentSensor_evt event)
{
    switch (event)
    {
    case CurrentSensor_evtReportCurrent:
    {
        CurrentSensorReport();
        break;
    }

    case CurrentSensor_evtNwkSetup:
    {
        if (false == serverSetup)
        {
            serverSetup = true;
            (void) setupCoapServer(OtInstance_get(), &coapAttr);
            reportingAddress = siteWideMulticastAddress;
            CurrentSensor_postEvt(CurrentSensor_evtAddressValid);
        }
        break;
    }

    case CurrentSensor_evtKeyRight:
    {
        if ((!otDatasetIsCommissioned(OtInstance_get()))
                && (OtStack_joinState() != OT_STACK_EVENT_NWK_JOIN_IN_PROGRESS))
        {
            OtStack_joinConfiguredNetwork();
        }
        break;
    }

    case CurrentSensor_evtNwkJoined:
    {
        (void) OtStack_setupNetwork();
        break;
    }

    case CurrentSensor_evtAddressValid:
    {
        CurrentSensorReport();
        break;
    }

    case CurrentSensor_evtNwkJoinFailure:
    {
        CurrentSensor_postEvt(CurrentSensor_evtKeyRight);
        break;
    }

    case CurrentSensor_evtDevRoleChanged:
    {
        OtRtosApi_lock();
        otDeviceRole role = otThreadGetDeviceRole(OtInstance_get());
        OtRtosApi_unlock();

        switch (role)
        {
        case OT_DEVICE_ROLE_DISABLED:
        case OT_DEVICE_ROLE_DETACHED:
        {
            hasParent = false;
            reportingAddress = siteWideMulticastAddress;
            CurrentSensor_postEvt(CurrentSensor_evtKeyRight);
            break;
        }
        case OT_DEVICE_ROLE_CHILD:
        case OT_DEVICE_ROLE_ROUTER:
        case OT_DEVICE_ROLE_LEADER:
        {
            hasParent = true;
#if ENABLE_BROADCAST_MODE
            reportingAddress = siteWideMulticastAddress;
#else
            getParentAddress(&reportingAddress);
#endif
            break;
        }

        default:
        {
            break;
        }
        }
    }
    default:
    {
        break;
    }
    }
}

/**
 * @brief Initializes the UART interface.
 *
 * This function sets up the UART with the desired parameters, such as baud rate,
 * and opens a UART instance for communication.
 */
void uart_init()
{
    // Initialize uart
    UART2_Params uartParams;
    UART2_Params_init(&uartParams);
    uartParams.baudRate = 115200;
    uart = UART2_open(CONFIG_UART2_0, &uartParams);
}

void uart_uninit()
{
    UART2_close(uart);
}

/**
 * @brief Powers on the device and enables data acquisition.
 *
 * This function initializes UART communication, toggles a GPIO pin to enable
 * the data acquisition process, and sets the device's power status to true.
 */
void powerOn()
{
    uart_init();

    ota_init();

    // Toggle GPIO to light up
    GPIO_toggle(CONFIG_GPIO_PWM_1);

    // Set the power status to true
    isPowered = true;

    // Set the polling period to the minimum allowed value to increase the frequency
    // of communication checks during the OTA process
    OtRtosApi_lock();
    otLinkSetPollPeriod(OtInstance_get(), OPENTHREAD_CONFIG_MAC_MINIMUM_POLL_PERIOD);
    OtRtosApi_unlock();

    // Ensure the task is not blocked by the semaphore when powering on
    continuousSleepActive = false;
    if (continuousSleepSem != NULL) {
        Semaphore_post(continuousSleepSem); // Wake up task if sleeping due to button
    }
}

/**
 * @brief Powers off the device and disables data acquisition.
 *
 * This function resets the data acquisition count, toggles a GPIO pin to disable
 * the data acquisition process, and sets the device's power status to false.
 */
void powerOff()
{
    uart_uninit();
    // Reset the data acquisition count to 0
    count = 0;

    // Toggle GPIO to turn off the light
    GPIO_toggle(CONFIG_GPIO_PWM_1);

    // Set the power status to false
    isPowered = false;

    // Set the polling period back to default
    OtRtosApi_lock();
    otLinkSetPollPeriod(OtInstance_get(), TIOP_CONFIG_POLL_PERIOD);
    OtRtosApi_unlock();

    // If config button is pressed when powering off, set flag for next power on
    if (GPIO_read(CONFIG_GPIO_SHUTDOWN) == 1)
    {
        continuousSleepActive = true;
    }
}

/**
 * @brief Button Interrupt Service Routine (ISR) handler for power button (CONFIG_GPIO_BUTTON_0_INPUT).
 *
 * This function handles button press and release events. It reads the button's
 * GPIO state and calls the appropriate function to power on or power off the device.
 *
 * @param index The index of the GPIO pin that triggered the interrupt.
 */
void buttonIsrHandler(uint_least8_t index)
{
    // Read the current state of the button GPIO pin
    uint32_t pinStatus = GPIO_read(CONFIG_GPIO_BUTTON_0_INPUT);

    // If the pin state is high, the button is pressed
    if (pinStatus == 1)
    {
        // Preventing jitter
        if(!isPowered) {
            powerOn();
        }
    }
    else
    {
        // Preventing jitter
        if(isPowered) {
            powerOff();
        }
    }
}

/**
 * @brief Interrupt Service Routine (ISR) handler for configuration button (CONFIG_GPIO_SHUTDOWN).
 *
 * Handles press/release for continuous sleep mode.
 *
 * @param index The index of the GPIO pin that triggered the interrupt.
 */
void configButtonIsrHandler(uint_least8_t index)
{
    // Read the current state of the button GPIO pin
    uint32_t pinStatus = GPIO_read(CONFIG_GPIO_SHUTDOWN);

    // If the pin state is high (1), the button is pressed -> activate sleep
    if (pinStatus == 1 && !isPowered)
    {
        continuousSleepActive = true;
    }
    else // If the pin state is low (0), the button is released -> deactivate sleep and wake task
    {
        if(continuousSleepActive) {
            continuousSleepActive = false;
            // Post the semaphore to wake up the task if it was sleeping
            if (continuousSleepSem != NULL) {
                Semaphore_post(continuousSleepSem);
            }
        }
    }
}

/**
 * @brief Configures the power button GPIO (CONFIG_GPIO_BUTTON_0_INPUT) and sets up its interrupt.
 *
 * This function configures the button as an input with an internal pull-down resistor
 * and enables interrupts on both rising and falling edges. It also sets a callback
 * for the button interrupt and initializes the UART if the button is pressed.
 */
void powerButton_init()
{
    // Configure the button GPIO as input with pull-down resistor and interrupt on both edges
    GPIO_setConfig(
            CONFIG_GPIO_BUTTON_0_INPUT,
            GPIO_CFG_INPUT | GPIO_CFG_IN_INT_BOTH_EDGES
                    | GPIO_CFG_PULL_DOWN_INTERNAL);
    GPIO_setCallback(CONFIG_GPIO_BUTTON_0_INPUT, buttonIsrHandler);
    GPIO_enableInt(CONFIG_GPIO_BUTTON_0_INPUT);

    // Read the initial state of the button GPIO pin
    uint32_t pinStatus = GPIO_read(CONFIG_GPIO_BUTTON_0_INPUT);

    // If the pin is high, the button is pressed
    if (pinStatus == 1)
    {
        powerOn();
    }
}

/**
 * @brief Configures the configuration button GPIO (CONFIG_GPIO_SHUTDOWN) and sets up its interrupt.
 *
 * Assumes pull-down resistor and interrupt on both edges.
 */
void configButton_init()
{
    // Configure the button GPIO as input with pull-down resistor and interrupt on both edges
    GPIO_setConfig(
            CONFIG_GPIO_SHUTDOWN,
            GPIO_CFG_INPUT | GPIO_CFG_IN_INT_BOTH_EDGES
                    | GPIO_CFG_PULL_DOWN_INTERNAL);
    GPIO_setCallback(CONFIG_GPIO_SHUTDOWN, configButtonIsrHandler);
    GPIO_enableInt(CONFIG_GPIO_SHUTDOWN);

    // Read the initial state of the button GPIO pin
    uint32_t pinStatus = GPIO_read(CONFIG_GPIO_SHUTDOWN);

    // If the pin is high (1) initially, activate continuous sleep
    if (pinStatus == 1 && !isPowered)
    {
        continuousSleepActive = true;
    }
}

/**
 * @brief Initializes the sensor settings.
 *
 * This function initializes the sensor state, including setting initial flags
 * and clearing the attribute buffer. The LED will flash during initialization
 * to indicate the boot process.
 */
void sensor_init()
{
    // Flash the LED to indicate that the device is booting
    turnLed();

    // Initialize status flags
    isPowered = false;
    isOTA = false;
    hasParent = false;
    continuousSleepActive = false; // Ensure sleep is inactive initially

    type = *((volatile uint8_t*)CONFIG_ADDRESS);

    // 如果是type=5，初始化LUT数据
    if (type == 5) {
        readLUTData();
    }

    // Clear the CoAP attribute status buffer
    memset(attrCurrent, 0, sizeof(attrCurrent));
}

/**
 * @brief 根据测量的微伏值计算电流
 *
 * 该函数根据传感器类型(type)选择对应的计算方法：
 * - 当type为5时，使用二次多项式函数计算电流，提供更高的准确性
 * - 当type为7时，使用0.0127欧姆分流电阻
 * - 其他type值默认使用type=7的电阻值
 *
 * @param microVolts 测量的微伏值
 * @return 计算出的电流值(单位：安培)
 */
float calculateCurrentFromMicroVolts(uint32_t microVolts)
{
    uint32_t avgMicroVolts;
    // 对type=5进行LUT校正
    if (type == 5) {
        avgMicroVolts = correct_uv(microVolts);
    }

    double currentA = 0.0;            /* 保持 double 计算精度 */

    /* ---------- type == 5 : 九次多项式标定 ---------- */
    if (type == 5)
    {
        /* 先把微伏缩放到 0-1 数量级，避免高次项溢出 */
        const double x = avgMicroVolts * 1e-6;     // 46926 µV → 0.046926

        /* 9th-order coefficients (重新标定所得) */
        const double a9 =  8.55625315e+05;
        const double a8 = -3.08898916e+06;
        const double a7 =  4.72003998e+06;
        const double a6 = -3.97855272e+06;
        const double a5 =  2.02041066e+06;
        const double a4 = -6.32564590e+05;
        const double a3 =  1.19128177e+05;
        const double a2 = -1.22209109e+04;
        const double a1 =  7.44855943e+02;
        const double a0 = -1.23369342e+01;

        /* 霍纳法则求值：(((((((((a9*x + a8)*x + a7)...)+a1)*x)+a0 */
        currentA = ((((((((a9 * x + a8) * x + a7) * x + a6) * x + a5)
                      * x + a4) * x + a3) * x + a2) * x + a1) * x + a0;

        if (currentA < 0.0)           /* 防止出现负值 */
            currentA = 0.0;
    }
    /* ---------- type == 7 & 其他 : 分流电阻法 ---------- */
    else
    {
        const double voltV  = microVolts * 1e-6;  // µV → V
        currentA = voltV / 0.0127;                // I = V / R
        if (currentA < 0.0)
            currentA = 0.0;
    }

    return (float)currentA;
}

/******************************************************************************
 External functions
 *****************************************************************************/

/*
 * Documented in Currentsensor.h
 */
void CurrentSensor_postEvt(CurrentSensor_evt event)
{
    struct CurrentSensor_procQueueMsg msg;
    int ret;
    msg.evt = event;

    ret = mq_send(CurrentSensor_procQueueDesc, (const char*) &msg, sizeof(msg),
                  0);
    assert(0 == ret);
    (void) ret;
}

/*
 * Documented in task_config.h
 */
void CurrentSensor_taskCreate(void)
{
    pthread_t thread;
    pthread_attr_t pAttrs;
    struct sched_param priParam;
    int retc;

    retc = pthread_attr_init(&pAttrs);
    assert(retc == 0);

    retc = pthread_attr_setdetachstate(&pAttrs, PTHREAD_CREATE_DETACHED);
    assert(retc == 0);

    priParam.sched_priority = TASK_CONFIG_CurrentSENSOR_TASK_PRIORITY;
    retc = pthread_attr_setschedparam(&pAttrs, &priParam);
    assert(retc == 0);

    retc = pthread_attr_setstack(&pAttrs, (void*) stack,
    TASK_CONFIG_CurrentSENSOR_TASK_STACK_SIZE);
    assert(retc == 0);

    retc = pthread_create(&thread, &pAttrs, CurrentSensor_task, NULL);
    assert(retc == 0);

    retc = pthread_attr_destroy(&pAttrs);
    assert(retc == 0);

    (void) retc;
}

void enterShutdown()
{
    otInstance *instance = OtInstance_get();

    // --- Prepare and send a shutdown packet ---
    // The sendToGateway function will reset value_index and clear the attrCurrent buffer.
    memset(attrCurrent, 0, sizeof(attrCurrent));

    // Manually add MAC address
    uint8_t value_index_shutdown = 0;
    uint32_t macPartMsb = HWREG(0xAF070);
    uint32_t macPartLsb = HWREG(0xAF070 + 4);
    uint64_t macAddress = ((uint64_t)macPartMsb << 32) | macPartLsb;
    attrCurrent[value_index_shutdown++] = (macAddress >> 56) & 0xFF;
    attrCurrent[value_index_shutdown++] = (macAddress >> 48) & 0xFF;
    attrCurrent[value_index_shutdown++] = (macAddress >> 40) & 0xFF;
    attrCurrent[value_index_shutdown++] = (macAddress >> 32) & 0xFF;
    attrCurrent[value_index_shutdown++] = (macAddress >> 24) & 0xFF;
    attrCurrent[value_index_shutdown++] = (macAddress >> 16) & 0xFF;
    attrCurrent[value_index_shutdown++] = (macAddress >> 8) & 0xFF;
    attrCurrent[value_index_shutdown++] = macAddress & 0xFF;

    // Set other metadata fields
    attrCurrent[56] = 0;
    attrCurrent[57] = type;
    attrCurrent[58] = firmware_major;
    attrCurrent[59] = firmware_minor;
    attrCurrent[60] = firmware_patch;
    attrCurrent[61] = 1; // Shutdown flag

    // 发送shutdown消息时也需要递增message_id
    message_id++;

    // 获取当前RSSI值并添加到数据包(shutdown消息也包含RSSI和message_id)
    int8_t rssi = getCurrentRssi(instance);
    attrCurrent[62] = rssi;

    // 添加message_id (4字节, 大端格式)
    attrCurrent[63] = (message_id >> 24) & 0xFF;
    attrCurrent[64] = (message_id >> 16) & 0xFF;
    attrCurrent[65] = (message_id >> 8) & 0xFF;
    attrCurrent[66] = message_id & 0xFF;

    // Reuse sendToGateway to send the packet.
    // This will send a confirmable message since isPowered is false.
    sendToGateway(instance);

    // Brief delay to allow the packet to be sent out by the radio
    usleep(200000);

    OtRtosApi_lock();
    otCoapStop(OtInstance_get());
    otThreadSetEnabled(OtInstance_get(), false);
    OtRtosApi_unlock();

    GPIO_disableInt(CONFIG_GPIO_BUTTON_0_INPUT);
    GPIO_clearInt(CONFIG_GPIO_BUTTON_0_INPUT);

    GPIO_disableInt(CONFIG_GPIO_SHUTDOWN);
    GPIO_clearInt(CONFIG_GPIO_SHUTDOWN);

    GPIO_setConfig(CONFIG_GPIO_SHUTDOWN, GPIO_CFG_IN_PU | GPIO_CFG_SHUTDOWN_WAKE_LOW);

    /* Go to shutdown */
    Power_shutdown(0, 0);
}

/**
 *  Current sensor processing thread
 */
void* CurrentSensor_task(void *arg0)
{
    PowerCC26X2_ResetReason resetReason = PowerCC26X2_getResetReason();

    /* If we are waking up from shutdown, we do something extra. */
    if (resetReason == PowerCC26X2_RESET_SHUTDOWN_IO)
    {
        PowerCC26X2_releaseLatches();
    }

    // Initializes the sensor settings.
    sensor_init();

    struct mq_attr attr;
    struct CurrentSensor_procQueueMsg msg; // Moved msg declaration outside loop
    mqd_t procQueueLoopDesc;

    attr.mq_curmsgs = 0;
    attr.mq_flags = 0;
    attr.mq_maxmsg = 20;
    attr.mq_msgsize = sizeof(struct CurrentSensor_procQueueMsg);

    /* Enable non-blocking mode for processing queues to notify callback functions */
    CurrentSensor_procQueueDesc = mq_open(CurrentSensor_procQueueName,
                                          (O_WRONLY | O_NONBLOCK | O_CREAT), 0,
                                          &attr);

    /* Turn on blocking read mode for the processing queue, used for processing loops */
    procQueueLoopDesc = mq_open(CurrentSensor_procQueueName, O_RDONLY, 0, NULL);

    // Enable AON Battery Monitor
    AONBatMonEnable();

    // Create OT stack task
    OtStack_taskCreate();

    // Register OT stack event callback function
    OtStack_registerCallback(processOtStackEvents);

    /* Set the polling period, as NVS does not store the polling period */
    OtRtosApi_lock();
    otLinkSetPollPeriod(OtInstance_get(), TIOP_CONFIG_POLL_PERIOD);
    otThreadSetChildTimeout(OtInstance_get(), 240);
    OtRtosApi_unlock();

    // Setting up interfaces and networks
    OtStack_setupInterfaceAndNetwork();

    OtRtosApi_lock();
    otIp6AddressFromString(TIOP_SITE_WIDE_MULTICAST_ADDRESS,
                           &siteWideMulticastAddress);
    // Convert IP address from string
    otIp6AddressFromString(TIOP_CurrentSENSOR_REPORTING_ADDRESS,
                           &reportingAddress);
    OtRtosApi_unlock();

    // Create the semaphore for continuous sleep mode
    Semaphore_Params semParams;
    Semaphore_Params_init(&semParams);
    semParams.mode = Semaphore_Mode_BINARY;
    // Pass NULL for the Error_Block parameter if detailed error reporting is not needed
    continuousSleepSem = Semaphore_create(0, &semParams, NULL);
    if (continuousSleepSem == NULL) {
        // Handle error - Semaphore creation failed
        // Log an error or assert
        assert(false); // Halt execution if semaphore creation fails
    }

    // Initialize the power button (original button)
    powerButton_init();
    // Initialize the configuration button (new button)
    configButton_init();

    while (true)
    {
        // Check if continuous sleep mode is active (triggered by CONFIG_BUTTON_1)
        if (continuousSleepActive && count == 0) // Only enter sleep mode when count is 0
        {
            enterShutdown();
            // Pend (wait) on the semaphore indefinitely.
            // The task will block here efficiently until the semaphore is posted
            // (by button release or powerOn).
            Semaphore_pend(continuousSleepSem, BIOS_WAIT_FOREVER);
            // Once woken up, loop back to re-check the continuousSleepActive flag.
            continue;
        }

        // If not in continuous sleep, wait for events from the message queue
        ssize_t ret;
        ret = mq_receive(procQueueLoopDesc, (char*) &msg, sizeof(msg), NULL);

        // Process received event
        if (ret >= 0 && ret == sizeof(msg))
        {
            processEvent(msg.evt);
        }
        // Handle potential errors from mq_receive if needed
    }

    return NULL; // Should technically not be reached
}

