#ifndef _CurrentSENSOR_H_
#define _CurrentSENSOR_H_

/******************************************************************************
     Constants and Definitions
 *****************************************************************************/

/* The address of the current value on the coap server */
#define CurrentSENSOR_Current_URI     "CurrentSensor/Currents"

/* The resource address sent by the coap server post */
#define sleepAndSc_CURRENT_URI     "CurrentSensor/Currents"

/**
 * Current sensor events
 */
typedef enum
{
    CurrentSensor_evtReportCurrent,        /* report timeout event */
    CurrentSensor_evtNwkSetup,             /* openthread network is setup */
    CurrentSensor_evtDevRoleChanged,       /* Events for Device State */
    CurrentSensor_evtAddressValid,         /* Global address registered, we may begin reporting */
    CurrentSensor_evtKeyRight,             /* Right key is pressed */
    CurrentSensor_evtNwkJoined,            /* Joined the network */
    CurrentSensor_evtNwkJoinFailure,       /* Failed joining network */

} CurrentSensor_evt;

/******************************************************************************
     External functions
 *****************************************************************************/

/**
 * @brief Posts an event to the Current Sensor task.
 *
 * @param event event to post.
 * @return None
 */
extern void CurrentSensor_postEvt(CurrentSensor_evt event);

#endif /* _CurrentSENSOR_H_ */
