#include <assert.h>
#include <stdint.h>
#include <stdio.h>

#include <ti/display/Display.h>
#include <ti/drivers/GPIO.h>
#include <ti/drivers/NVS.h>
#include <ti/sysbios/BIOS.h>

#include <ti/drivers/AESECB.h>
#include <ti/drivers/SHA2.h>
#include <ti/drivers/ECJPAKE.h>
#include <ti/drivers/power/PowerCC26X2.h>

#include "ti_drivers_config.h"
#include "common/task_config.h"

int main(void)

{
    Board_initGeneral();

    AESECB_init();

    SHA2_init();

    // Starting current sensor
    CurrentSensor_taskCreate();

    BIOS_start();

    return (0);
}



