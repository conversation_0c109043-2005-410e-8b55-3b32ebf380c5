# HYP-60 Current Sensor

*[中文文档](README_zh.md)*

## Technical Overview

The HYP-60 is a wireless current sensor device based on the Texas Instruments CC2652R7 microcontroller. It continuously monitors current using an ADC (Analog-to-Digital Converter) and transmits the readings to a gateway device over a Thread mesh network using the CoAP protocol.

### Main Features

- Current sensing with 12-bit ADC resolution
- Thread mesh networking based on OpenThread protocol
- CoAP communication for data transmission
- Over-The-Air (OTA) firmware upgrade capability
- Low power mode for extended battery life
- Dual button operation for power control and configuration

### Hardware Specifications

- **Microcontroller**: TI CC2652R7
- **Wireless**: 2.4GHz IEEE 802.15.4 radio
- **ADC Resolution**: 12-bit
- **Reference Voltage**: 3.3V
- **Memory**:
  - Flash: ~350KB (Main application: 0x00000080-0x55F80, OTA Storage: 0x56000-0xAC000)
  - RAM: 144KB (0x20000000-0x24000)
- **Buttons**:
  - Power button (CONFIG_GPIO_BUTTON_0_INPUT)
  - Configuration button (CONFIG_GPIO_SHUTDOWN)
- **Interfaces**: UART for debugging (115200 baud)

### Software Architecture

- **RTOS**: TI-RTOS 7
- **Network Stack**: OpenThread (Thread protocol)
- **Communication Protocol**: CoAP
- **Security**: SHA-256 for firmware verification
- **Firmware Version**: Major.Minor.Patch format

## Getting Started

### Initial Setup

1. Install required development tools
   - [TI Uniflash](https://www.ti.com/tool/UNIFLASH#downloads) for firmware flashing
   - [Code Composer Studio](https://www.ti.com/tool/CCSTUDIO) (optional, for development)

2. Ensure you have a Thread Border Router/Gateway configured for your network

3. Connect the HYP-60 device to the programming interface

### Programming the Device

#### Method 1: Using Uniflash (Recommended)

1. Download Uniflash from [TI.com](https://www.ti.com/tool/UNIFLASH#downloads)

2. Launch Uniflash and select "LP-CC2652R7" from the device list, then click "Start"

   ![Select LP-CC2652R7](https://github.com/user-attachments/assets/96d8eb1e-c882-420d-afd7-43829ebb342f)

3. Use "Browse" to select the HYP-60.bin file located in the root directory of the project

4. Click "Load Image" to flash the firmware to the device

   ![Load Image](https://github.com/user-attachments/assets/8054d8b8-adc1-426f-ab1a-fffe82c24951)

#### Method 2: Using UART

1. Download sblAppEx: http://www.ti.com/lit/zip/swra466

2. Copy HYP-60.bin and mcuboot_LP_CC2652R7_nortos_ticlang.bin to the sblAppEx_1_04_00_00/bin folder

3. Open the sblAppEx.sln in the sblAppEx_1_04_00_00/ide/win32/msvc2015 folder using Visual Studio

4. Replace the original sblAppEx.cpp with the one from the HYP-60 bin folder

5. Comment out lines 691 and 692 of the code for burning different firmwares

6. Build and run the application to flash the firmware

## Device Operation

### Basic Operation

1. **Power On/Off**: Press the power button to turn the device on or off
   - When powered on, the device attempts to join the Thread network
   - The LED toggles to indicate power status

2. **Normal Operation**: Once connected to the network, the device samples current readings and sends them to the gateway at regular intervals
   - Default sleep time between readings is 4 seconds
   - The sleep time can be adjusted via CoAP responses from the gateway

3. **Low Power Mode**: The device enters low power mode between readings to conserve energy

4. **Configuration Mode**: Press and hold the configuration button when powering off to enter continuous sleep mode
   - The device will remain in low power mode until awakened

### Network Joining

The device automatically attempts to join the Thread network when powered on. If it fails to join, it will retry periodically.

## Over-The-Air (OTA) Updates

The HYP-60 supports firmware updates over the Thread network:

1. Ensure your gateway contains the latest sensor firmware

2. Power on the sensor and it will automatically request firmware version information from the gateway

3. If a newer firmware version is available, the device will download it automatically

4. The OTA process takes approximately 5 minutes

5. The device verifies the downloaded firmware using SHA-256 hash verification before applying

6. After successful verification, the device reboots to apply the new firmware

## Troubleshooting

- **Device won't join network**: Ensure your Thread Border Router is operational and within range
- **OTA update fails**: Check that the gateway has the correct firmware version and that the device is connected to the network
- **Device powers off unexpectedly**: Check battery level, the device may be entering low power mode

## Development and Customization

### Flash Memory Layout

- **Main Application**: 0x00000080 - 0x55F80
- **OTA Storage**: 0x56000 - 0xAC000
- **Configuration Data**: 0xAFFFC

### Firmware Versioning

The firmware version follows Major.Minor.Patch format. The current version can be found in the currentsensor.c file:

```c
static uint8_t firmware_major = 0;
static uint8_t firmware_minor = 0;
static uint8_t firmware_patch = 10;
```

### Building Custom Firmware

1. Install Code Composer Studio and TI SimpleLink SDK
2. Configure the project for LP-CC2652R7 with TI-RTOS 7
3. Modify the source files as needed
4. Build the project to generate a new binary
5. Flash the binary using one of the methods described above

## License

This project is distributed under the terms specified in the accompanying license file.
