<?xml version="1.0" encoding="UTF-8"?>
<projectDescription>
	<name>HYP-60</name>
	<comment></comment>
	<projects>
		<project>libopenthread_mtd_cc13x2_26x2_ticlang</project>
		<project>libopenthread_platform_utils_mtd_cc13x2_26x2_ticlang</project>
		<project>libmbedcrypto_cc13x2_26x2_ticlang</project>
	</projects>
	<buildSpec>
		<buildCommand>
			<name>org.eclipse.cdt.managedbuilder.core.genmakebuilder</name>
			<triggers>clean,full,incremental,</triggers>
			<arguments>
			</arguments>
		</buildCommand>
		<buildCommand>
			<name>org.eclipse.cdt.managedbuilder.core.ScannerConfigBuilder</name>
			<triggers>full,incremental,</triggers>
			<arguments>
			</arguments>
		</buildCommand>
	</buildSpec>
	<natures>
		<nature>com.ti.ccstudio.core.ccsNature</nature>
		<nature>org.eclipse.cdt.core.cnature</nature>
		<nature>org.eclipse.cdt.managedbuilder.core.managedBuildNature</nature>
		<nature>org.eclipse.cdt.core.ccnature</nature>
		<nature>org.eclipse.cdt.managedbuilder.core.ScannerConfigNature</nature>
	</natures>
</projectDescription>
