/******************************************************************************/
#ifndef _TASK_CONFIG_H_
#define _TASK_CONFIG_H_

#ifdef __cplusplus
extern "C"
{
#endif

/******************************************************************************
 Constants and definitions
 *****************************************************************************/
/**
 * Priority of the OpenThread Stack task.
 */
#ifndef TASK_CONFIG_OT_TASK_PRIORITY
#define TASK_CONFIG_OT_TASK_PRIORITY    1
#endif

/**
 * Size of the OpenThread Stack task call stack.
 *
 * NOTE: last stack peak tested at 3280 bytes (conformance test 8.2.1 CCS
 *       toolchain 2020-6-9). Adding 20% and rounding to largest 1K for round
 *       numbers.
 */
#ifndef TASK_CONFIG_OT_TASK_STACK_SIZE
#define TASK_CONFIG_OT_TASK_STACK_SIZE  4096
#endif

/**
 * Priority of the Application task.
 */
#ifndef TASK_CONFIG_CurrentSENSOR_TASK_PRIORITY
#define TASK_CONFIG_CurrentSENSOR_TASK_PRIORITY   2
#endif

/**
 * Size of the cli task call stack.
 */
#ifndef TASK_CONFIG_CurrentSENSOR_TASK_STACK_SIZE
#define TASK_CONFIG_CurrentSENSOR_TASK_STACK_SIZE 2048
#endif

/**
 * ��otstack��Currentsensorͬʱ����˯��ģʽ���ź���
 */
extern bool isSleep;

/******************************************************************************
 External functions
 *****************************************************************************/

/**
 * Creation funciton for the OpenThread Stack task.
 */
extern void OtStack_taskCreate(void);

/**
 * Creation funciton for the Currenterature sensor application task.
 */
extern void CurrentSensor_taskCreate(void);

#ifdef __cplusplus
}
#endif

#endif /* _TASK_CONFIG_H_ */

