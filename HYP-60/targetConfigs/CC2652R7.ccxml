<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<configurations XML_version="1.2" id="configurations_0">
<configuration XML_version="1.2" id="Texas Instruments XDS110 USB Debug Probe_0">
        <instance XML_version="1.2" desc="Texas Instruments XDS110 USB Debug Probe_0" href="connections/TIXDS110_Connection.xml" id="Texas Instruments XDS110 USB Debug Probe_0" xml="TIXDS110_Connection.xml" xmlpath="connections"/>
        <connection XML_version="1.2" id="Texas Instruments XDS110 USB Debug Probe_0">
            <instance XML_version="1.2" href="drivers/tixds510icepick_c.xml" id="drivers" xml="tixds510icepick_c.xml" xmlpath="drivers"/>
            <instance XML_version="1.2" href="drivers/tixds510cs_dap.xml" id="drivers" xml="tixds510cs_dap.xml" xmlpath="drivers"/>
            <instance XML_version="1.2" href="drivers/tixds510cortexM.xml" id="drivers" xml="tixds510cortexM.xml" xmlpath="drivers"/>
            <property Type="choicelist" Value="1" id="Power Selection">
                <choice Name="Probe supplied power" value="1">
                    <property Type="stringfield" Value="3.3" id="Voltage Level"/>
                </choice>
            </property>
            <property Type="choicelist" Value="0" id="JTAG Signal Isolation"/>
            <property Type="choicelist" Value="4" id="SWD Mode Settings">
                <choice Name="cJTAG (1149.7) 2-pin advanced modes" value="enable">
                    <property Type="choicelist" Value="1" id="XDS110 Aux Port"/>
                <property Type="choicelist" Value="1" id="Target Scan Format"/>
                </choice>
            </property>
            <property Type="choicelist" Value="1" id="Debug Probe Selection">
                <choice Name="Select by serial number" value="0">
                    <property Type="stringfield" Value="L15000WK" id="-- Enter the serial number"/>
                </choice>
            </property>
            <property Type="choicelist" Value="0" id="The JTAG TCLK Frequency (MHz)">
                <choice Name="Fixed with user specified value" value="SPECIFIC">
                    <property Type="stringfield" Value="1MHz" id="-- Enter a value from 100.0kHz to 2.5MHz"/>
                </choice>
            </property>
            <platform XML_version="1.2" id="platform_0">
                <instance XML_version="1.2" desc="CC2652R7_0" href="devices/cc2652r7.xml" id="CC2652R7_0" xml="cc2652r7.xml" xmlpath="devices"/>
            </platform>
        </connection>
    </configuration>
</configurations>