<?xml version="1.0" encoding="UTF-8" ?>
<?ccsproject version="1.0"?>
<projectOptions>
	<ccsVersion value="11.0.0"/>
	<deviceVariant value="Cortex M.CC2652R7"/>
	<deviceFamily value="TMS470"/>
	<deviceEndianness value="little"/>
	<codegenToolVersion value="TICLANG_1.3.0.LTS"/>
	<isElfFormat value="true"/>
	<connection value="common/targetdb/connections/TIXDS110_Connection.xml"/>
	<rts value=""/>
	<createSlaveProjects value=""/>
	<ignoreDefaultDeviceSettings value="true"/>
	<ignoreDefaultCCSSettings value="true"/>
	<templateProperties value="id=temp_sensor_LP_CC2652R7_tirtos7_ticlang.projectspec.temp_sensor_LP_CC2652R7_tirtos7_ticlang,buildProfile=release,isHybrid=true"/>
	<origin value="C:\ti\simplelink_cc13xx_cc26xx_sdk_6_41_00_17\examples\rtos\LP_CC2652R7\thread\temp_sensor\tirtos7\ticlang\temp_sensor_LP_CC2652R7_tirtos7_ticlang.projectspec"/>
	<filesToOpen value=""/>
	<sourceLookupPath value=""/>
	<isTargetManual value="false"/>
	<activeTargetConfiguration value="targetConfigs/CC2652R7.ccxml"/>
</projectOptions>
