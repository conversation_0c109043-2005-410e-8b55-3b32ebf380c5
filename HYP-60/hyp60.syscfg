/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --board "/ti/boards/LP_CC2652R7" --rtos "tirtos7" --product "simplelink_cc13xx_cc26xx_sdk@**********"
 * @versions {"tool":"1.16.2+3028"}
 */

/**
 * Import the modules used in this configuration.
 */
const CCFG        = scripting.addModule("/ti/devices/CCFG");
const rfdesign    = scripting.addModule("/ti/devices/radioconfig/rfdesign");
const ADCBuf      = scripting.addModule("/ti/drivers/ADCBuf", {}, false);
const ADCBuf1     = ADCBuf.addInstance();
const AESECB      = scripting.addModule("/ti/drivers/AESECB");
const AESECB1     = AESECB.addInstance();
const DMA         = scripting.addModule("/ti/drivers/DMA");
const ECDH        = scripting.addModule("/ti/drivers/ECDH");
const ECDH1       = ECDH.addInstance();
const ECDSA       = scripting.addModule("/ti/drivers/ECDSA");
const ECDSA1      = ECDSA.addInstance();
const ECJPAKE     = scripting.addModule("/ti/drivers/ECJPAKE");
const ECJPAKE1    = ECJPAKE.addInstance();
const GPIO        = scripting.addModule("/ti/drivers/GPIO", {}, false);
const GPIO2       = GPIO.addInstance();
const GPIO3       = GPIO.addInstance();
const NVS         = scripting.addModule("/ti/drivers/NVS");
const NVS1        = NVS.addInstance();
const PWM         = scripting.addModule("/ti/drivers/PWM", {}, false);
const PWM1        = PWM.addInstance();
const Power       = scripting.addModule("/ti/drivers/Power");
const RF          = scripting.addModule("/ti/drivers/RF");
const SHA2        = scripting.addModule("/ti/drivers/SHA2");
const SHA21       = SHA2.addInstance();
const TRNG        = scripting.addModule("/ti/drivers/TRNG");
const TRNG1       = TRNG.addInstance();
const UART2       = scripting.addModule("/ti/drivers/UART2", {}, false);
const UART21      = UART2.addInstance();
const Button      = scripting.addModule("/ti/drivers/apps/Button", {}, false);
const Button1     = Button.addInstance();
const Settings    = scripting.addModule("/ti/posix/tirtos/Settings");
const BIOS        = scripting.addModule("/ti/sysbios/BIOS");
const Event       = scripting.addModule("/ti/sysbios/knl/Event");
const Idle        = scripting.addModule("/ti/sysbios/knl/Idle", {}, false);
const Idle2       = Idle.addInstance();
const Mailbox     = scripting.addModule("/ti/sysbios/knl/Mailbox");
const Error       = scripting.addModule("/ti/sysbios/runtime/Error");
const SysCallback = scripting.addModule("/ti/sysbios/runtime/SysCallback");
const Timestamp   = scripting.addModule("/ti/sysbios/runtime/Timestamp");
const thread      = scripting.addModule("/ti/thread/thread");

/**
 * Write custom configuration values to the imported modules.
 */
CCFG.levelBootloaderBackdoor  = "Active low";
CCFG.enableBootloaderBackdoor = true;
CCFG.enableBootloader         = true;
CCFG.dioBootloaderBackdoor    = 7;
CCFG.ccfgTemplate.$name       = "ti_devices_CCFG_CCFGCC26XXTemplate0";

ADCBuf1.$name                             = "CONFIG_ADCBUF_0";
ADCBuf1.timerInstance.$name               = "CONFIG_GPTIMER_0";
ADCBuf1.adcBufChannel0.$name              = "ADCBUF_CHANNEL_0";
ADCBuf1.adcBufChannel0.adc.adcPin.$assign = "boosterpack.23";

AESECB1.$name = "CONFIG_AESECB_0";

ECDH1.$name = "CONFIG_ECDH_0";

ECDSA1.$name = "CONFIG_ECDSA_0";

ECJPAKE1.$name = "CONFIG_ECJPAKE_0";

GPIO2.$name           = "CONFIG_GPIO_0";
GPIO2.mode            = "Output";
GPIO2.gpioPin.$assign = "boosterpack.29";

GPIO3.$name           = "CONFIG_GPIO_SHUTDOWN";
GPIO3.gpioPin.$assign = "boosterpack.18";
scripting.suppress("Connected to hardware,@@@.+?@@@ is connected to LaunchPad SPI Bus Chip Select on the CC2652R7 LaunchPad\\. Consider selecting it in 'use hardware' above\\. @@@.+?@@@", GPIO3, "gpioPin");

NVS1.$name                    = "CONFIG_NVSINTERNAL";
NVS1.internalFlash.$name      = "ti_drivers_nvs_NVSCC26XX0";
NVS1.internalFlash.regionBase = 0x52000;
NVS1.internalFlash.regionSize = 0x4000;

PWM1.$name                                         = "CONFIG_PWM_0";
PWM1.$hardware                                     = system.deviceData.board.components.LED_RED;
PWM1.timerObject.$name                             = "CONFIG_GPTIMER_1";
PWM1.timerObject.pwmPinInstance.initialOutputState = "High";

SHA21.$name = "CONFIG_SHA2_0";

TRNG1.$name = "CONFIG_TRNG_0";

UART21.$name     = "CONFIG_UART2_0";
UART21.$hardware = system.deviceData.board.components.XDS110UART;

Button1.$name          = "CONFIG_BUTTON_0";
Button1.polarity       = "Active High";
Button1.button.$assign = "boosterpack.32";
Button1.gpioPin.pull   = "Pull Up";

BIOS.assertsEnabled = false;
BIOS.heapBaseAddr   = "__primary_heap_start__";
BIOS.heapEndAddr    = "__primary_heap_end__";

const Hwi           = scripting.addModule("/ti/sysbios/family/arm/m3/Hwi", {}, false);
Hwi.enableException = false;

const Clock      = scripting.addModule("/ti/sysbios/knl/Clock", {}, false);
Clock.tickPeriod = 10;

const Timer = scripting.addModule("/ti/sysbios/family/arm/cc26xx/Timer", {}, false);

Idle2.$name   = "powerIdle";
Idle2.idleFxn = "Power_idleFunc";

const Semaphore            = scripting.addModule("/ti/sysbios/knl/Semaphore", {}, false);
Semaphore.supportsPriority = false;

const Swi         = scripting.addModule("/ti/sysbios/knl/Swi", {}, false);
Swi.numPriorities = 6;

const Task             = scripting.addModule("/ti/sysbios/knl/Task", {}, false);
Task.checkStackFlag    = false;
Task.defaultStackSize  = 512;
Task.idleTaskStackSize = 512;
Task.numPriorities     = 6;

Error.policy       = "Error_SPIN";
Error.printDetails = false;

const System           = scripting.addModule("/ti/sysbios/runtime/System", {}, false);
System.abortFxn        = "System_abortSpin";
System.exitFxn         = "System_exitSpin";
System.extendedFormats = "%f";
System.supportModule   = "SysCallback";

thread.deviceType                            = "mtd";
thread.deviceTypeReadOnly                    = true;
thread.pm.$name                              = "ti_thread_pm_thread_pm0";
thread.pm.pollPeriod                         = 55000;
thread.rf.$name                              = "ti_thread_rf_thread_rf0";
thread.rf.txpower                            = 5;
thread.rf.radioConfig.$name                  = "ti_devices_radioconfig_settings_ieee_15_40";
thread.rf.radioConfig.codeExportConfig.$name = "ti_devices_radioconfig_code_export_param0";
thread.network.$name                         = "ti_thread_network_thread_network0";
thread.network.networkKey                    = "0x00112233445566778899aabbccddeeff";
thread.network.panID                         = "0xface";
thread.security.$name                        = "ti_thread_security_thread_security0";
thread.security.pskd                         = "TMPSENS1";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
ADCBuf1.adc.$suggestSolution                   = "ADC0";
ADCBuf1.adc.dmaADCChannel.$suggestSolution     = "DMA_CH7";
ADCBuf1.timerInstance.timer.$suggestSolution   = "GPTM0";
ADCBuf1.adcBufChannel0.adc.$suggestSolution    = "ADC0";
PWM1.timerObject.timer.$suggestSolution        = "GPTM1";
PWM1.timerObject.timer.pwmPin.$suggestSolution = "boosterpack.39";
UART21.uart.$suggestSolution                   = "UART0";
UART21.uart.txPin.$suggestSolution             = "boosterpack.4";
UART21.uart.rxPin.$suggestSolution             = "boosterpack.3";
Timer.rtc.$suggestSolution                     = "RTC0";
