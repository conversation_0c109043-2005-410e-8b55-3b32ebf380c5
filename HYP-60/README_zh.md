# HYP-60 电流传感器

*[English Documentation](README.md)*

## 技术概述

HYP-60 是一款基于德州仪器 CC2652R7 微控制器的无线电流传感器设备。它通过 ADC（模数转换器）持续监测电流，并通过基于 CoAP 协议的 Thread 网状网络将读数传输到网关设备。

### 主要特点

- 采用 12 位 ADC 分辨率的电流感测
- 基于 OpenThread 协议的 Thread 网状网络
- 使用 CoAP 协议进行数据传输
- 支持空中固件升级（OTA）
- 低功耗模式延长电池寿命
- 双按钮操作用于电源控制和配置

### 硬件规格

- **微控制器**：TI CC2652R7
- **无线通信**：2.4GHz IEEE 802.15.4 无线电
- **ADC 分辨率**：12 位
- **参考电压**：3.3V
- **内存**：
  - 闪存：约 350KB（主应用程序：0x00000080-0x55F80，OTA 存储：0x56000-0xAC000）
  - RAM：144KB（0x20000000-0x24000）
- **按钮**：
  - 电源按钮（CONFIG_GPIO_BUTTON_0_INPUT）
  - 配置按钮（CONFIG_GPIO_SHUTDOWN）
- **接口**：用于调试的 UART（115200 波特率）

### 软件架构

- **RTOS**：TI-RTOS 7
- **网络协议栈**：OpenThread（Thread 协议）
- **通信协议**：CoAP
- **安全性**：使用 SHA-256 进行固件验证
- **固件版本**：采用 主版本.次版本.补丁 格式

## 入门指南

### 初始设置

1. 安装所需的开发工具
   - [TI Uniflash](https://www.ti.com/tool/UNIFLASH#downloads) 用于固件烧录
   - [Code Composer Studio](https://www.ti.com/tool/CCSTUDIO)（可选，用于开发）

2. 确保您已为网络配置了 Thread 边界路由器/网关

3. 将 HYP-60 设备连接到编程接口

### 设备编程

#### 方法 1：使用 Uniflash（推荐）

1. 从 [TI.com](https://www.ti.com/tool/UNIFLASH#downloads) 下载 Uniflash

2. 启动 Uniflash 并从设备列表中选择"LP-CC2652R7"，然后点击"Start"

   ![选择 LP-CC2652R7](https://github.com/user-attachments/assets/96d8eb1e-c882-420d-afd7-43829ebb342f)

3. 使用"Browse"选择位于项目根目录的 HYP-60.bin 文件

4. 点击"Load Image"将固件烧录到设备

   ![加载镜像](https://github.com/user-attachments/assets/8054d8b8-adc1-426f-ab1a-fffe82c24951)

#### 方法 2：使用 UART

1. 下载 sblAppEx：http://www.ti.com/lit/zip/swra466

2. 将 HYP-60.bin 和 mcuboot_LP_CC2652R7_nortos_ticlang.bin 复制到 sblAppEx_1_04_00_00/bin 文件夹

3. 使用 Visual Studio 打开 sblAppEx_1_04_00_00/ide/win32/msvc2015 文件夹中的 sblAppEx.sln

4. 用 HYP-60 bin 文件夹中的 sblAppEx.cpp 替换原始的 sblAppEx.cpp

5. 注释掉第 691 行和第 692 行代码，以便烧录不同的固件

6. 构建并运行应用程序以烧录固件

## 设备操作

### 基本操作

1. **开关机**：按下电源按钮打开或关闭设备
   - 开机后，设备尝试加入 Thread 网络
   - LED 灯会切换状态以指示电源状态

2. **正常操作**：一旦连接到网络，设备会定期采样电流读数并将其发送到网关
   - 读数之间的默认休眠时间为 4 秒
   - 休眠时间可以通过网关的 CoAP 响应进行调整

3. **低功耗模式**：设备在读数之间进入低功耗模式以节省能源

4. **配置模式**：关机时按住配置按钮可进入连续休眠模式
   - 设备将保持低功耗模式直到被唤醒

### 网络加入

设备在开机后会自动尝试加入 Thread 网络。如果加入失败，它将定期重试。

## 空中升级（OTA）

HYP-60 支持通过 Thread 网络进行固件更新：

1. 确保您的网关包含最新的传感器固件

2. 打开传感器，它将自动从网关请求固件版本信息

3. 如果有较新的固件版本可用，设备将自动下载

4. OTA 过程大约需要 5 分钟

5. 设备在应用前会使用 SHA-256 哈希验证下载的固件

6. 验证成功后，设备重启以应用新固件

## 故障排除

- **设备无法加入网络**：确保您的 Thread 边界路由器正常运行且在范围内
- **OTA 更新失败**：检查网关是否具有正确的固件版本，以及设备是否已连接到网络
- **设备意外关机**：检查电池电量，设备可能正在进入低功耗模式

## 开发和定制

### 闪存布局

- **主应用程序**：0x00000080 - 0x55F80
- **OTA 存储**：0x56000 - 0xAC000
- **配置数据**：0xAFFFC

### 固件版本控制

固件版本采用 主版本.次版本.补丁 格式。当前版本可以在 currentsensor.c 文件中找到：

```c
static uint8_t firmware_major = 0;
static uint8_t firmware_minor = 0;
static uint8_t firmware_patch = 10;
```

### 构建自定义固件

1. 安装 Code Composer Studio 和 TI SimpleLink SDK
2. 为 LP-CC2652R7 和 TI-RTOS 7 配置项目
3. 根据需要修改源文件
4. 构建项目以生成新的二进制文件
5. 使用上述方法之一烧录二进制文件

## 许可证

该项目根据随附许可证文件中指定的条款分发。 